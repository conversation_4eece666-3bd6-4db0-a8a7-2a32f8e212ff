# 多线程人脸比对服务使用说明

## 概述

多线程人脸比对服务是基于com.hl.face包开发的高性能人脸识别比对服务，支持传递多个相机ID和时间范围进行批量人脸比对，具有以下特点：

- **多线程并发处理**：支持多线程并行处理多个相机的数据
- **详细日志记录**：提供完整的处理进度和详细的操作日志
- **实时进度跟踪**：可以实时查询任务执行进度
- **异步任务执行**：支持异步和同步两种执行模式
- **错误处理机制**：完善的异常处理和错误恢复机制

## API接口

### 1. 启动人脸比对任务

**接口地址：** `POST /face/compare/start`

**请求参数：**
```json
{
  "cameraIds": ["camera1", "camera2", "camera3"],
  "startTimestamp": 1640995200,
  "endTimestamp": 1641081600,
  "threadCount": 10,
  "batchSize": 100,
  "async": true
}
```

**参数说明：**
- `cameraIds`: 相机ID列表（必填）
- `startTimestamp`: 开始时间戳，秒级（必填）
- `endTimestamp`: 结束时间戳，秒级（必填）
- `threadCount`: 线程数量（可选，默认10）
- `batchSize`: 批次大小（可选，默认100）
- `async`: 是否异步执行（可选，默认true）

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "RUNNING",
    "startTime": "2024-01-01T10:00:00",
    "totalPictures": 0,
    "processedPictures": 0,
    "successCount": 0,
    "failedCount": 0,
    "progressPercentage": 0.0
  }
}
```

### 2. 查询任务进度

**接口地址：** `GET /face/compare/progress/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "RUNNING",
    "startTime": "2024-01-01T10:00:00",
    "endTime": null,
    "totalPictures": 1000,
    "processedPictures": 350,
    "successCount": 320,
    "failedCount": 30,
    "progressPercentage": 35.0,
    "cameraStats": {
      "camera1": {
        "cameraId": "camera1",
        "cameraName": "大门口摄像头",
        "totalPictures": 500,
        "processedPictures": 200,
        "successCount": 180,
        "failedCount": 20,
        "progress": 40.0,
        "status": "RUNNING"
      }
    }
  }
}
```

### 3. 停止任务

**接口地址：** `POST /face/compare/stop/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "任务停止成功",
  "data": true
}
```

### 4. 获取任务结果

**接口地址：** `GET /face/compare/result/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "COMPLETED",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T10:30:00",
    "totalPictures": 1000,
    "processedPictures": 1000,
    "successCount": 950,
    "failedCount": 50,
    "progressPercentage": 100.0,
    "results": [
      {
        "picId": "pic123",
        "cameraId": "camera1",
        "cameraName": "大门口摄像头",
        "faceImageId": "face456",
        "name": "张三",
        "idCard": "110101199001011234",
        "similarity": "95.6",
        "appearTime": "2024-01-01T08:30:00",
        "compareTime": "2024-01-01T10:15:00",
        "compareStatus": "SUCCESS"
      }
    ]
  }
}
```

### 5. 获取活跃任务列表

**接口地址：** `GET /face/compare/active`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "task1",
      "status": "RUNNING",
      "progressPercentage": 45.0
    },
    {
      "taskId": "task2",
      "status": "RUNNING",
      "progressPercentage": 78.5
    }
  ]
}
```

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
face-compare:
  compare-picture:
    enable: true
    cluster-id: WJSPW_HJ_1639380209
  thread-pool:
    core-pool-size: 20      # 核心线程数
    max-pool-size: 50       # 最大线程数
    queue-capacity: 1000    # 队列容量
    keep-alive-seconds: 60  # 线程空闲时间
```

## 使用示例

### Java代码示例

```java
@Autowired
private MultiFaceCompareService multiFaceCompareService;

public void startFaceCompare() {
    // 创建请求
    FaceCompareRequestDTO request = new FaceCompareRequestDTO();
    request.setCameraIds(Arrays.asList("camera1", "camera2", "camera3"));
    request.setStartTimestamp(1640995200L);
    request.setEndTimestamp(1641081600L);
    request.setThreadCount(10);
    request.setBatchSize(100);
    request.setAsync(true);
    
    // 启动任务
    FaceCompareResponseVO response = multiFaceCompareService.startFaceCompareTask(request);
    String taskId = response.getTaskId();
    
    // 轮询查询进度
    while (true) {
        FaceCompareResponseVO progress = multiFaceCompareService.getTaskProgress(taskId);
        System.out.println("进度: " + progress.getProgressPercentage() + "%");
        
        if ("COMPLETED".equals(progress.getStatus()) || "FAILED".equals(progress.getStatus())) {
            break;
        }
        
        Thread.sleep(5000); // 等待5秒
    }
}
```

### curl命令示例

```bash
# 启动任务
curl -X POST http://localhost:38081/face/compare/start \
  -H "Content-Type: application/json" \
  -d '{
    "cameraIds": ["camera1", "camera2"],
    "startTimestamp": 1640995200,
    "endTimestamp": 1641081600,
    "threadCount": 5,
    "batchSize": 50,
    "async": true
  }'

# 查询进度
curl http://localhost:38081/face/compare/progress/abc123def456

# 停止任务
curl -X POST http://localhost:38081/face/compare/stop/abc123def456

# 获取结果
curl http://localhost:38081/face/compare/result/abc123def456
```

## 日志说明

服务提供详细的日志记录，包括：

- 任务启动和完成日志
- 每个相机的处理进度
- 每张图片的处理状态
- 错误和异常信息
- 性能统计信息

日志级别建议设置为INFO，关键操作会记录在INFO级别，详细的处理过程在DEBUG级别。

## 注意事项

1. **资源消耗**：多线程处理会消耗较多CPU和内存资源，请根据服务器配置调整线程数量
2. **网络依赖**：服务依赖依图API，请确保网络连接稳定
3. **数据库连接**：大量并发可能导致数据库连接池耗尽，请适当调整连接池配置
4. **任务管理**：建议定期清理已完成的任务以释放内存
5. **错误处理**：对于失败的图片，服务会记录错误信息但不会中断整个任务
