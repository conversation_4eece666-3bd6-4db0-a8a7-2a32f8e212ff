# 批量数据获取和人脸比对服务使用说明

## 概述

批量数据获取和人脸比对服务是基于com.hl.face包开发的综合性人脸识别服务，支持传递多个相机ID和时间范围进行批量数据获取，然后自动进行多线程人脸比对，具有以下特点：

- **批量数据获取**：支持从多个相机批量获取指定时间范围内的图片数据
- **智能去重**：自动检测和跳过已存在的图片数据
- **多线程并发处理**：支持多线程并行处理多个相机的数据获取和比对
- **详细日志记录**：提供完整的处理进度和详细的操作日志
- **实时进度跟踪**：可以实时查询任务执行进度
- **异步任务执行**：支持异步和同步两种执行模式
- **自动人脸比对**：数据获取完成后可自动启动人脸比对任务
- **错误处理机制**：完善的异常处理和错误恢复机制

## API接口

### 1. 启动批量数据获取任务

**接口地址：** `POST /face/batch/fetch/start`

**请求参数：**
```json
{
  "cameraIds": ["camera1", "camera2", "camera3"],
  "startTimestamp": 1640995200,
  "endTimestamp": 1641081600,
  "immediateCompare": true,
  "threadCount": 10,
  "batchSize": 100,
  "maxPages": 50,
  "async": true
}
```

**参数说明：**
- `cameraIds`: 相机ID列表（必填）
- `startTimestamp`: 开始时间戳，秒级（必填）
- `endTimestamp`: 结束时间戳，秒级（必填）
- `immediateCompare`: 是否立即进行人脸比对，默认true（可选）
- `threadCount`: 线程数量，默认10（可选）
- `batchSize`: 批次大小，默认100（可选）
- `maxPages`: 最大获取页数限制，默认50（可选）
- `async`: 是否异步执行，默认true（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "RUNNING",
    "startTime": "2024-01-01T10:00:00",
    "totalCameras": 3,
    "processedCameras": 0,
    "totalPictures": 0,
    "newPictures": 0,
    "duplicatePictures": 0,
    "progressPercentage": 0.0,
    "compareTaskStarted": false
  }
}
```

### 2. 启动综合任务（数据获取+人脸比对）

**接口地址：** `POST /face/compare/batch-fetch-and-compare`

这是一个综合接口，会先进行批量数据获取，然后自动启动人脸比对任务。

**请求参数：** 与批量数据获取接口相同

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "RUNNING",
    "startTime": "2024-01-01T10:00:00",
    "totalCameras": 3,
    "processedCameras": 1,
    "totalPictures": 150,
    "newPictures": 120,
    "duplicatePictures": 30,
    "progressPercentage": 33.3,
    "compareTaskStarted": true,
    "compareTaskId": "xyz789uvw012",
    "cameraStats": {
      "camera1": {
        "cameraId": "camera1",
        "cameraName": "大门口摄像头",
        "fetchedPages": 5,
        "totalPictures": 150,
        "newPictures": 120,
        "duplicatePictures": 30,
        "progress": 100.0,
        "status": "COMPLETED",
        "latestTimestamp": 1641081500
      }
    }
  }
}
```

### 3. 查询任务进度

**接口地址：** `GET /face/batch/fetch/progress/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "RUNNING",
    "startTime": "2024-01-01T10:00:00",
    "endTime": null,
    "totalCameras": 3,
    "processedCameras": 2,
    "totalPictures": 500,
    "newPictures": 450,
    "duplicatePictures": 50,
    "progressPercentage": 66.7,
    "compareTaskStarted": true,
    "compareTaskId": "xyz789uvw012",
    "cameraStats": {
      "camera1": {
        "cameraId": "camera1",
        "cameraName": "大门口摄像头",
        "fetchedPages": 8,
        "totalPictures": 200,
        "newPictures": 180,
        "duplicatePictures": 20,
        "progress": 100.0,
        "status": "COMPLETED",
        "latestTimestamp": 1641081500
      },
      "camera2": {
        "cameraId": "camera2",
        "cameraName": "后门摄像头",
        "fetchedPages": 6,
        "totalPictures": 150,
        "newPictures": 140,
        "duplicatePictures": 10,
        "progress": 100.0,
        "status": "COMPLETED",
        "latestTimestamp": 1641081400
      },
      "camera3": {
        "cameraId": "camera3",
        "cameraName": "楼梯口摄像头",
        "fetchedPages": 3,
        "totalPictures": 150,
        "newPictures": 130,
        "duplicatePictures": 20,
        "progress": 60.0,
        "status": "RUNNING"
      }
    }
  }
}
```

### 4. 停止任务

**接口地址：** `POST /face/batch/fetch/stop/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "任务停止成功",
  "data": true
}
```

### 5. 获取任务结果

**接口地址：** `GET /face/batch/fetch/result/{taskId}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "abc123def456",
    "status": "COMPLETED",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T10:15:00",
    "totalCameras": 3,
    "processedCameras": 3,
    "totalPictures": 800,
    "newPictures": 720,
    "duplicatePictures": 80,
    "progressPercentage": 100.0,
    "compareTaskStarted": true,
    "compareTaskId": "xyz789uvw012"
  }
}
```

### 6. 获取活跃任务列表

**接口地址：** `GET /face/batch/fetch/active`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "task1",
      "status": "RUNNING",
      "progressPercentage": 45.0,
      "totalCameras": 5,
      "processedCameras": 2
    },
    {
      "taskId": "task2",
      "status": "RUNNING",
      "progressPercentage": 78.5,
      "totalCameras": 3,
      "processedCameras": 2
    }
  ]
}
```

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
face-compare:
  get-picture:
    enable: true
    cluster-id: WJSPW_HJ_1639380209
  compare-picture:
    enable: true
    cluster-id: WJSPW_HJ_1639380209
  thread-pool:
    core-pool-size: 20      # 核心线程数
    max-pool-size: 50       # 最大线程数
    queue-capacity: 1000    # 队列容量
    keep-alive-seconds: 60  # 线程空闲时间
```

## 使用示例

### Java代码示例

```java
@Autowired
private BatchDataFetchService batchDataFetchService;

public void startBatchDataFetch() {
    // 创建请求
    BatchDataFetchRequestDTO request = new BatchDataFetchRequestDTO();
    request.setCameraIds(Arrays.asList("camera1", "camera2", "camera3"));
    request.setStartTimestamp(1640995200L);
    request.setEndTimestamp(1641081600L);
    request.setImmediateCompare(true);
    request.setThreadCount(10);
    request.setBatchSize(100);
    request.setMaxPages(50);
    request.setAsync(true);
    
    // 启动任务
    BatchDataFetchResponseVO response = batchDataFetchService.startBatchDataFetchTask(request);
    String taskId = response.getTaskId();
    
    // 轮询查询进度
    while (true) {
        BatchDataFetchResponseVO progress = batchDataFetchService.getTaskProgress(taskId);
        System.out.println("数据获取进度: " + progress.getProgressPercentage() + "%");
        System.out.println("新增图片数: " + progress.getNewPictures());
        System.out.println("比对任务ID: " + progress.getCompareTaskId());
        
        if ("COMPLETED".equals(progress.getStatus()) || "FAILED".equals(progress.getStatus())) {
            break;
        }
        
        Thread.sleep(5000); // 等待5秒
    }
}
```

### curl命令示例

```bash
# 启动批量数据获取和比对任务
curl -X POST http://localhost:38081/face/compare/batch-fetch-and-compare \
  -H "Content-Type: application/json" \
  -d '{
    "cameraIds": ["camera1", "camera2"],
    "startTimestamp": 1640995200,
    "endTimestamp": 1641081600,
    "immediateCompare": true,
    "threadCount": 5,
    "batchSize": 50,
    "maxPages": 20,
    "async": true
  }'

# 查询任务进度
curl -X GET http://localhost:38081/face/batch/fetch/progress/abc123def456

# 停止任务
curl -X POST http://localhost:38081/face/batch/fetch/stop/abc123def456

# 查询活跃任务
curl -X GET http://localhost:38081/face/batch/fetch/active
```

## 任务状态说明

- **RUNNING**: 任务正在执行中
- **COMPLETED**: 任务已完成
- **FAILED**: 任务执行失败
- **STOPPED**: 任务被手动停止

## 注意事项

1. **时间范围**: 开始时间必须小于结束时间，时间戳为秒级
2. **相机ID**: 必须是系统中已配置且启用的相机ID
3. **线程数量**: 建议根据服务器性能调整，避免设置过大导致系统负载过高
4. **批次大小**: 影响每次API调用获取的数据量，建议保持默认值100
5. **最大页数**: 防止单个相机获取过多数据，可根据实际需求调整
6. **异步执行**: 建议使用异步模式，避免长时间阻塞请求

## 错误处理

服务提供了完善的错误处理机制：

- 参数验证错误会返回400状态码
- 任务不存在会返回404状态码
- 系统内部错误会返回500状态码
- 所有错误都会记录详细的日志信息

## 性能优化建议

1. 合理设置线程数量，避免过多线程导致系统负载过高
2. 根据网络状况调整批次大小和最大页数
3. 定期清理已完成的任务记录
4. 监控系统资源使用情况，及时调整配置参数
