package com.hl.face.service;

import com.hl.face.domain.dto.FaceCompareRequestDTO;
import com.hl.face.domain.vo.FaceCompareResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 多线程人脸比对服务测试
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest
@Slf4j
public class MultiFaceCompareServiceTest {
    
    @Autowired
    private MultiFaceCompareService multiFaceCompareService;
    
    /**
     * 测试启动人脸比对任务
     */
    @Test
    public void testStartFaceCompareTask() {
        // 创建测试请求
        FaceCompareRequestDTO request = new FaceCompareRequestDTO();
        request.setCameraIds(Arrays.asList("test_camera_1", "test_camera_2"));
        request.setStartTimestamp(1640995200L); // 2022-01-01 00:00:00
        request.setEndTimestamp(1641081600L);   // 2022-01-02 00:00:00
        request.setThreadCount(5);
        request.setBatchSize(50);
        request.setAsync(true);
        
        try {
            // 启动任务
            FaceCompareResponseVO response = multiFaceCompareService.startFaceCompareTask(request);
            
            log.info("任务启动成功:");
            log.info("任务ID: {}", response.getTaskId());
            log.info("任务状态: {}", response.getStatus());
            log.info("开始时间: {}", response.getStartTime());
            
            // 等待一段时间后查询进度
            Thread.sleep(2000);
            
            FaceCompareResponseVO progress = multiFaceCompareService.getTaskProgress(response.getTaskId());
            log.info("任务进度:");
            log.info("总图片数: {}", progress.getTotalPictures());
            log.info("已处理数: {}", progress.getProcessedPictures());
            log.info("成功数: {}", progress.getSuccessCount());
            log.info("失败数: {}", progress.getFailedCount());
            log.info("进度百分比: {}%", progress.getProgressPercentage());
            
            // 打印相机统计
            if (progress.getCameraStats() != null) {
                progress.getCameraStats().forEach((cameraId, stats) -> {
                    log.info("相机 {} 统计: 总数={}, 已处理={}, 成功={}, 失败={}, 进度={}%", 
                            cameraId, stats.getTotalPictures(), stats.getProcessedPictures(),
                            stats.getSuccessCount(), stats.getFailedCount(), stats.getProgress());
                });
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
    
    /**
     * 测试获取活跃任务
     */
    @Test
    public void testGetActiveTasks() {
        try {
            List<FaceCompareResponseVO> activeTasks = multiFaceCompareService.getActiveTasks();
            log.info("当前活跃任务数量: {}", activeTasks.size());
            
            for (FaceCompareResponseVO task : activeTasks) {
                log.info("任务ID: {}, 状态: {}, 进度: {}%", 
                        task.getTaskId(), task.getStatus(), task.getProgressPercentage());
            }
            
        } catch (Exception e) {
            log.error("获取活跃任务失败", e);
        }
    }
    
    /**
     * 测试参数验证
     */
    @Test
    public void testParameterValidation() {
        // 测试空相机列表
        try {
            FaceCompareRequestDTO request = new FaceCompareRequestDTO();
            request.setCameraIds(Arrays.asList());
            request.setStartTimestamp(1640995200L);
            request.setEndTimestamp(1641081600L);
            
            multiFaceCompareService.startFaceCompareTask(request);
            log.error("应该抛出异常但没有抛出");
            
        } catch (IllegalArgumentException e) {
            log.info("参数验证正常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("意外异常", e);
        }
        
        // 测试时间范围错误
        try {
            FaceCompareRequestDTO request = new FaceCompareRequestDTO();
            request.setCameraIds(Arrays.asList("test_camera"));
            request.setStartTimestamp(1641081600L);
            request.setEndTimestamp(1640995200L); // 结束时间小于开始时间
            
            multiFaceCompareService.startFaceCompareTask(request);
            log.error("应该抛出异常但没有抛出");
            
        } catch (IllegalArgumentException e) {
            log.info("时间范围验证正常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("意外异常", e);
        }
    }
}
