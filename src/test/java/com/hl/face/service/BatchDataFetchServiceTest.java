package com.hl.face.service;

import com.hl.face.domain.dto.BatchDataFetchRequestDTO;
import com.hl.face.domain.vo.BatchDataFetchResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 批量数据获取服务测试
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest
@Slf4j
public class BatchDataFetchServiceTest {
    
    @Autowired
    private BatchDataFetchService batchDataFetchService;
    
    /**
     * 测试启动批量数据获取任务
     */
    @Test
    public void testStartBatchDataFetchTask() {
        // 创建测试请求
        BatchDataFetchRequestDTO request = new BatchDataFetchRequestDTO();
        request.setCameraIds(Arrays.asList("test_camera_1", "test_camera_2"));
        request.setStartTimestamp(1640995200L); // 2022-01-01 00:00:00
        request.setEndTimestamp(1641081600L);   // 2022-01-02 00:00:00
        request.setImmediateCompare(true);
        request.setThreadCount(5);
        request.setBatchSize(50);
        request.setMaxPages(10);
        request.setAsync(true);
        
        try {
            // 启动任务
            BatchDataFetchResponseVO response = batchDataFetchService.startBatchDataFetchTask(request);
            
            log.info("批量数据获取任务启动成功:");
            log.info("任务ID: {}", response.getTaskId());
            log.info("任务状态: {}", response.getStatus());
            log.info("开始时间: {}", response.getStartTime());
            log.info("总相机数: {}", response.getTotalCameras());
            log.info("是否启动比对: {}", response.getCompareTaskStarted());
            
            // 等待一段时间后查询进度
            Thread.sleep(3000);
            
            BatchDataFetchResponseVO progress = batchDataFetchService.getTaskProgress(response.getTaskId());
            log.info("任务进度:");
            log.info("已处理相机数: {}", progress.getProcessedCameras());
            log.info("总图片数: {}", progress.getTotalPictures());
            log.info("新增图片数: {}", progress.getNewPictures());
            log.info("重复图片数: {}", progress.getDuplicatePictures());
            log.info("进度百分比: {}%", progress.getProgressPercentage());
            log.info("比对任务ID: {}", progress.getCompareTaskId());
            
            // 查询相机统计
            if (progress.getCameraStats() != null) {
                progress.getCameraStats().forEach((cameraId, stats) -> {
                    log.info("相机 {} 统计: 状态={}, 新增图片={}, 重复图片={}, 进度={}%", 
                            cameraId, stats.getStatus(), stats.getNewPictures(), 
                            stats.getDuplicatePictures(), stats.getProgress());
                });
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
    
    /**
     * 测试查询活跃任务
     */
    @Test
    public void testGetActiveTasks() {
        try {
            List<BatchDataFetchResponseVO> activeTasks = batchDataFetchService.getActiveTasks();
            log.info("当前活跃的批量数据获取任务数量: {}", activeTasks.size());
            
            for (BatchDataFetchResponseVO task : activeTasks) {
                log.info("任务ID: {}, 状态: {}, 进度: {}%", 
                        task.getTaskId(), task.getStatus(), task.getProgressPercentage());
            }
            
        } catch (Exception e) {
            log.error("查询活跃任务失败", e);
        }
    }
    
    /**
     * 测试停止任务
     */
    @Test
    public void testStopTask() {
        // 先启动一个任务
        BatchDataFetchRequestDTO request = new BatchDataFetchRequestDTO();
        request.setCameraIds(Arrays.asList("test_camera_1"));
        request.setStartTimestamp(1640995200L);
        request.setEndTimestamp(1641081600L);
        request.setImmediateCompare(false);
        request.setAsync(true);
        
        try {
            BatchDataFetchResponseVO response = batchDataFetchService.startBatchDataFetchTask(request);
            String taskId = response.getTaskId();
            log.info("启动测试任务: {}", taskId);
            
            // 等待一段时间
            Thread.sleep(1000);
            
            // 停止任务
            boolean stopped = batchDataFetchService.stopTask(taskId);
            log.info("停止任务结果: {}", stopped);
            
            // 再次查询任务状态
            try {
                BatchDataFetchResponseVO progress = batchDataFetchService.getTaskProgress(taskId);
                log.info("停止后任务状态: {}", progress.getStatus());
            } catch (RuntimeException e) {
                log.info("任务已被移除: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("测试停止任务失败", e);
        }
    }
    
    /**
     * 测试参数验证
     */
    @Test
    public void testValidation() {
        // 测试空相机列表
        try {
            BatchDataFetchRequestDTO request = new BatchDataFetchRequestDTO();
            request.setCameraIds(Arrays.asList());
            request.setStartTimestamp(1640995200L);
            request.setEndTimestamp(1641081600L);
            
            batchDataFetchService.startBatchDataFetchTask(request);
            log.error("应该抛出异常但没有抛出");
            
        } catch (IllegalArgumentException e) {
            log.info("正确捕获到参数验证异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("意外的异常", e);
        }
        
        // 测试时间范围错误
        try {
            BatchDataFetchRequestDTO request = new BatchDataFetchRequestDTO();
            request.setCameraIds(Arrays.asList("test_camera_1"));
            request.setStartTimestamp(1641081600L);
            request.setEndTimestamp(1640995200L); // 结束时间小于开始时间
            
            batchDataFetchService.startBatchDataFetchTask(request);
            log.error("应该抛出异常但没有抛出");
            
        } catch (IllegalArgumentException e) {
            log.info("正确捕获到时间范围验证异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("意外的异常", e);
        }
    }
}
