package com.hl.face.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.face.domain.WjFaceCameraInfo;
import com.hl.face.domain.WjFacePicInfo;
import com.hl.face.domain.WjFaceYtRecord;
import com.hl.face.domain.dto.FaceCompareRequestDTO;
import com.hl.face.domain.vo.FaceCompareResponseVO;
import com.hl.face.domain.vo.YTCompareResultVO;
import com.hl.face.service.MultiFaceCompareService;
import com.hl.face.service.WjFaceCameraInfoService;
import com.hl.face.service.WjFacePicInfoService;
import com.hl.face.service.WjFaceYtRecordService;
import com.hl.face.utils.CompareYTApi;
import com.hl.face.utils.YTApiUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 多线程人脸比对服务实现
 * 
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class MultiFaceCompareServiceImpl implements MultiFaceCompareService {
    
    private final WjFaceCameraInfoService wjFaceCameraInfoService;
    private final WjFacePicInfoService wjFacePicInfoService;
    private final WjFaceYtRecordService wjFaceYtRecordService;

    private final Executor faceCompareExecutor;
    private final Executor pictureDownloadExecutor;

    public MultiFaceCompareServiceImpl(WjFaceCameraInfoService wjFaceCameraInfoService,
                                      WjFacePicInfoService wjFacePicInfoService,
                                      WjFaceYtRecordService wjFaceYtRecordService,
                                      @Qualifier("faceCompareExecutor") Executor faceCompareExecutor,
                                      @Qualifier("pictureDownloadExecutor") Executor pictureDownloadExecutor) {
        this.wjFaceCameraInfoService = wjFaceCameraInfoService;
        this.wjFacePicInfoService = wjFacePicInfoService;
        this.wjFaceYtRecordService = wjFaceYtRecordService;
        this.faceCompareExecutor = faceCompareExecutor;
        this.pictureDownloadExecutor = pictureDownloadExecutor;
    }
    
    @Value("${face-compare.compare-picture.cluster-id}")
    private String clusterId;
    
    @Value("${face-compare.compare-picture.enable:true}")
    private Boolean isCompareEnabled;
    
    // 任务存储
    private final Map<String, FaceCompareTask> activeTasks = new ConcurrentHashMap<>();
    private final Map<String, FaceCompareResponseVO> completedTasks = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        log.info("多线程人脸比对服务初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("正在关闭多线程人脸比对服务...");
        // 停止所有活跃任务
        activeTasks.values().forEach(task -> {
            try {
                task.stop();
            } catch (Exception e) {
                log.error("停止任务失败: {}", task.getTaskId(), e);
            }
        });
        activeTasks.clear();
        log.info("多线程人脸比对服务已关闭");
    }
    
    @Override
    public FaceCompareResponseVO startFaceCompareTask(FaceCompareRequestDTO request) {
        if (!isCompareEnabled) {
            throw new RuntimeException("人脸比对功能未启用");
        }
        
        // 生成任务ID
        String taskId = IdUtil.simpleUUID();
        log.info("开始启动人脸比对任务，任务ID: {}", taskId);
        
        // 验证参数
        validateRequest(request);
        
        // 创建任务
        FaceCompareTask task = new FaceCompareTask(taskId, request);
        activeTasks.put(taskId, task);
        
        // 创建响应对象
        FaceCompareResponseVO response = new FaceCompareResponseVO();
        response.setTaskId(taskId);
        response.setStatus("RUNNING");
        response.setStartTime(new Date());
        response.setProgressPercentage(0.0);
        
        // 异步执行任务
        if (request.getAsync()) {
            CompletableFuture.runAsync(() -> executeTask(task), faceCompareExecutor);
            log.info("任务 {} 已提交到异步执行队列", taskId);
        } else {
            executeTask(task);
        }
        
        return response;
    }
    
    @Override
    public FaceCompareResponseVO getTaskProgress(String taskId) {
        FaceCompareTask task = activeTasks.get(taskId);
        if (task != null) {
            return task.getProgress();
        }
        
        // 检查已完成的任务
        FaceCompareResponseVO completedTask = completedTasks.get(taskId);
        if (completedTask != null) {
            return completedTask;
        }
        
        throw new RuntimeException("任务不存在: " + taskId);
    }
    
    @Override
    public boolean stopTask(String taskId) {
        FaceCompareTask task = activeTasks.get(taskId);
        if (task != null) {
            log.info("正在停止任务: {}", taskId);
            task.stop();
            activeTasks.remove(taskId);
            return true;
        }
        return false;
    }
    
    @Override
    public FaceCompareResponseVO getTaskResult(String taskId) {
        return getTaskProgress(taskId);
    }
    
    @Override
    public List<FaceCompareResponseVO> getActiveTasks() {
        return activeTasks.values().stream()
                .map(FaceCompareTask::getProgress)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(FaceCompareRequestDTO request) {
        if (request.getCameraIds() == null || request.getCameraIds().isEmpty()) {
            throw new IllegalArgumentException("相机ID列表不能为空");
        }
        
        if (request.getStartTimestamp() == null || request.getEndTimestamp() == null) {
            throw new IllegalArgumentException("时间范围不能为空");
        }
        
        if (request.getStartTimestamp() >= request.getEndTimestamp()) {
            throw new IllegalArgumentException("开始时间必须小于结束时间");
        }
        
        // 验证相机ID是否存在
        List<String> validCameraIds = wjFaceCameraInfoService.getCameraIds();
        List<String> invalidCameraIds = request.getCameraIds().stream()
                .filter(id -> !validCameraIds.contains(id))
                .collect(Collectors.toList());
        
        if (!invalidCameraIds.isEmpty()) {
            throw new IllegalArgumentException("无效的相机ID: " + String.join(", ", invalidCameraIds));
        }
    }
    
    /**
     * 执行任务
     */
    private void executeTask(FaceCompareTask task) {
        try {
            log.info("开始执行人脸比对任务: {}", task.getTaskId());
            task.execute();

            // 任务完成，移动到已完成列表
            FaceCompareResponseVO result = task.getProgress();
            completedTasks.put(task.getTaskId(), result);
            activeTasks.remove(task.getTaskId());

            log.info("人脸比对任务完成: {}, 总处理: {}, 成功: {}, 失败: {}",
                    task.getTaskId(), result.getProcessedPictures(),
                    result.getSuccessCount(), result.getFailedCount());

        } catch (Exception e) {
            log.error("执行人脸比对任务失败: {}", task.getTaskId(), e);
            task.markAsFailed(e.getMessage());

            // 移动到已完成列表
            FaceCompareResponseVO result = task.getProgress();
            completedTasks.put(task.getTaskId(), result);
            activeTasks.remove(task.getTaskId());
        }
    }

    /**
     * 人脸比对任务类
     */
    private class FaceCompareTask {
        private final String taskId;
        private final FaceCompareRequestDTO request;
        private final AtomicInteger totalPictures = new AtomicInteger(0);
        private final AtomicInteger processedPictures = new AtomicInteger(0);
        private final AtomicInteger successCount = new AtomicInteger(0);
        private final AtomicInteger failedCount = new AtomicInteger(0);
        private final Date startTime = new Date();
        private Date endTime;
        private volatile boolean stopped = false;
        private String errorMessage;
        private final Map<String, FaceCompareResponseVO.CameraProcessStats> cameraStats = new ConcurrentHashMap<>();
        private final List<FaceCompareResponseVO.FaceCompareResultVO> results = new CopyOnWriteArrayList<>();

        public FaceCompareTask(String taskId, FaceCompareRequestDTO request) {
            this.taskId = taskId;
            this.request = request;

            // 初始化相机统计
            for (String cameraId : request.getCameraIds()) {
                FaceCompareResponseVO.CameraProcessStats stats = new FaceCompareResponseVO.CameraProcessStats();
                stats.setCameraId(cameraId);
                stats.setTotalPictures(0);
                stats.setProcessedPictures(0);
                stats.setSuccessCount(0);
                stats.setFailedCount(0);
                stats.setProgress(0.0);
                stats.setStatus("PENDING");
                cameraStats.put(cameraId, stats);
            }
        }

        public String getTaskId() {
            return taskId;
        }

        public void stop() {
            this.stopped = true;
            log.info("任务 {} 已标记为停止", taskId);
        }

        public void markAsFailed(String errorMessage) {
            this.errorMessage = errorMessage;
            this.endTime = new Date();
        }

        /**
         * 执行任务
         */
        public void execute() {
            log.info("任务 {} 开始执行，相机数量: {}, 时间范围: {} - {}",
                    taskId, request.getCameraIds().size(),
                    DateUtil.date(request.getStartTimestamp() * 1000),
                    DateUtil.date(request.getEndTimestamp() * 1000));

            try {
                // 获取登录信息
                String sessionId = YTApiUtils.getSessionId();
                JSONObject loginResult = CompareYTApi.getSessionId();

                if (StringUtils.isBlank(sessionId)) {
                    throw new RuntimeException("获取YT API会话失败");
                }

                log.info("任务 {} 获取API会话成功", taskId);

                // 为每个相机创建处理任务
                List<CompletableFuture<Void>> cameraFutures = new ArrayList<>();

                for (String cameraId : request.getCameraIds()) {
                    if (stopped) {
                        log.info("任务 {} 已停止，跳过相机 {}", taskId, cameraId);
                        break;
                    }

                    CompletableFuture<Void> cameraFuture = CompletableFuture.runAsync(() ->
                            processCameraData(cameraId, sessionId, loginResult), faceCompareExecutor);
                    cameraFutures.add(cameraFuture);
                }

                // 等待所有相机处理完成
                CompletableFuture.allOf(cameraFutures.toArray(new CompletableFuture[0])).join();

                this.endTime = new Date();
                log.info("任务 {} 执行完成", taskId);

            } catch (Exception e) {
                log.error("任务 {} 执行失败", taskId, e);
                throw e;
            }
        }

        /**
         * 处理单个相机的数据
         */
        private void processCameraData(String cameraId, String sessionId, JSONObject loginResult) {
            log.info("任务 {} 开始处理相机 {}", taskId, cameraId);

            FaceCompareResponseVO.CameraProcessStats stats = cameraStats.get(cameraId);
            stats.setStatus("RUNNING");

            try {
                // 获取相机信息
                WjFaceCameraInfo cameraInfo = wjFaceCameraInfoService.getOne(
                        Wrappers.<WjFaceCameraInfo>lambdaQuery().eq(WjFaceCameraInfo::getCameraId, cameraId));

                if (cameraInfo != null) {
                    stats.setCameraName(cameraInfo.getName());
                }

                // 分页获取图片数据
                int pageSize = request.getBatchSize();
                int offset = 0;
                int cameraTotal = 0;
                AtomicInteger cameraProcessed = new AtomicInteger();
                AtomicInteger cameraSuccess = new AtomicInteger();
                AtomicInteger cameraFailed = new AtomicInteger();

                while (!stopped) {
                    // 查询图片数据
                    List<WjFacePicInfo> picInfoList = wjFacePicInfoService.list(
                            Wrappers.<WjFacePicInfo>lambdaQuery()
                                    .eq(WjFacePicInfo::getCameraId, cameraId)
                                    .ge(WjFacePicInfo::getTimestamp, request.getStartTimestamp())
                                    .le(WjFacePicInfo::getTimestamp, request.getEndTimestamp())
                                    .orderByAsc(WjFacePicInfo::getTimestamp)
                                    .last("LIMIT " + pageSize + " OFFSET " + offset)
                    );

                    if (picInfoList.isEmpty()) {
                        log.info("任务 {} 相机 {} 没有更多数据，处理完成", taskId, cameraId);
                        break;
                    }

                    cameraTotal += picInfoList.size();
                    totalPictures.addAndGet(picInfoList.size());
                    stats.setTotalPictures(cameraTotal);

                    log.info("任务 {} 相机 {} 获取到 {} 张图片，开始处理", taskId, cameraId, picInfoList.size());

                    // 并行处理图片
                    List<CompletableFuture<Void>> pictureFutures = new ArrayList<>();

                    for (WjFacePicInfo picInfo : picInfoList) {
                        if (stopped) {
                            break;
                        }

                        CompletableFuture<Void> pictureFuture = CompletableFuture.runAsync(() -> {
                            try {
                                processSinglePicture(picInfo, sessionId, loginResult, stats);
                                cameraSuccess.getAndIncrement();
                                successCount.incrementAndGet();
                            } catch (Exception e) {
                                log.error("任务 {} 处理图片失败: {}", taskId, picInfo.getId(), e);
                                cameraFailed.getAndIncrement();
                                failedCount.incrementAndGet();
                            } finally {
                                cameraProcessed.getAndIncrement();
                                processedPictures.incrementAndGet();
                                stats.setProcessedPictures(cameraProcessed);
                                stats.setSuccessCount(cameraSuccess);
                                stats.setFailedCount(cameraFailed);
                                stats.setProgress(cameraTotal > 0 ? (double) cameraProcessed / cameraTotal * 100 : 0);
                            }
                        }, faceCompareExecutor);

                        pictureFutures.add(pictureFuture);
                    }

                    // 等待当前批次处理完成
                    CompletableFuture.allOf(pictureFutures.toArray(new CompletableFuture[0])).join();

                    offset += pageSize;

                    log.info("任务 {} 相机 {} 当前批次处理完成，已处理: {}/{}",
                            taskId, cameraId, cameraProcessed, cameraTotal);
                }

                stats.setStatus("COMPLETED");
                log.info("任务 {} 相机 {} 处理完成，总计: {}, 成功: {}, 失败: {}",
                        taskId, cameraId, cameraProcessed, cameraSuccess, cameraFailed);

            } catch (Exception e) {
                stats.setStatus("FAILED");
                log.error("任务 {} 相机 {} 处理失败", taskId, cameraId, e);
                throw e;
            }
        }

        /**
         * 处理单张图片
         */
        private void processSinglePicture(WjFacePicInfo picInfo, String sessionId,
                                         JSONObject loginResult, FaceCompareResponseVO.CameraProcessStats stats) {
            log.debug("任务 {} 开始处理图片ID: {}", taskId, picInfo.getId());

            try {
                // 检查是否已经比对过
                WjFaceYtRecord existingRecord = wjFaceYtRecordService.getOne(
                        Wrappers.<WjFaceYtRecord>lambdaQuery().eq(WjFaceYtRecord::getPicId, picInfo.getId()));

                if (existingRecord != null) {
                    log.debug("任务 {} 图片ID: {} 已存在比对记录，跳过", taskId, picInfo.getId());

                    // 添加到结果列表
                    FaceCompareResponseVO.FaceCompareResultVO result = createResultVO(picInfo, existingRecord, stats);
                    results.add(result);
                    return;
                }

                // 下载人脸图片
                String globalFaceImageUri = picInfo.getGlobalFaceImageUri();
                log.debug("任务 {} 图片ID: {} 开始下载人脸图片", taskId, picInfo.getId());

                String downloadedPicture = YTApiUtils.downloadPicture(sessionId, clusterId, globalFaceImageUri);

                if (StringUtils.isBlank(downloadedPicture)) {
                    log.warn("任务 {} 图片ID: {} 下载人脸图片失败", taskId, picInfo.getId());
                    throw new RuntimeException("下载人脸图片失败");
                }

                log.debug("任务 {} 图片ID: {} 人脸图片下载成功", taskId, picInfo.getId());

                // 进行人脸比对
                log.debug("任务 {} 图片ID: {} 开始进行人脸比对", taskId, picInfo.getId());
                YTCompareResultVO resultVO = CompareYTApi.comparePicture(loginResult, downloadedPicture);
                log.debug("任务 {} 图片ID: {} 人脸比对完成，相似度: {}", taskId, picInfo.getId(), resultVO.getSimilarity());

                // 保存比对结果
                WjFaceYtRecord wjFaceYtRecord = new WjFaceYtRecord();
                wjFaceYtRecord.setPicId(picInfo.getId());
                wjFaceYtRecord.setFaceImageId(picInfo.getFaceImageId());
                wjFaceYtRecord.setName(resultVO.getName());
                wjFaceYtRecord.setIdCard(resultVO.getIdCard());
                wjFaceYtRecord.setCreateTime(new Date());
                wjFaceYtRecord.setAppearTime(DateUtil.date(picInfo.getTimestamp() * 1000));
                wjFaceYtRecord.setSimilarity(resultVO.getSimilarity());
                wjFaceYtRecord.setCompareResult(resultVO.getCompareResult());

                wjFaceYtRecordService.save(wjFaceYtRecord);

                // 添加到结果列表
                FaceCompareResponseVO.FaceCompareResultVO result = createResultVO(picInfo, wjFaceYtRecord, stats);
                result.setCompareStatus("SUCCESS");
                results.add(result);

                log.debug("任务 {} 图片ID: {} 处理完成", taskId, picInfo.getId());

            } catch (Exception e) {
                log.error("任务 {} 处理图片ID: {} 失败", taskId, picInfo.getId(), e);

                // 添加失败结果
                FaceCompareResponseVO.FaceCompareResultVO result = new FaceCompareResponseVO.FaceCompareResultVO();
                result.setPicId(picInfo.getId());
                result.setCameraId(picInfo.getCameraId());
                result.setCameraName(stats.getCameraName());
                result.setFaceImageId(picInfo.getFaceImageId());
                result.setAppearTime(DateUtil.date(picInfo.getTimestamp() * 1000));
                result.setCompareTime(new Date());
                result.setCompareStatus("FAILED");
                result.setErrorMessage(e.getMessage());
                results.add(result);

                throw e;
            }
        }

        /**
         * 创建结果VO
         */
        private FaceCompareResponseVO.FaceCompareResultVO createResultVO(WjFacePicInfo picInfo,
                                                                        WjFaceYtRecord record,
                                                                        FaceCompareResponseVO.CameraProcessStats stats) {
            FaceCompareResponseVO.FaceCompareResultVO result = new FaceCompareResponseVO.FaceCompareResultVO();
            result.setPicId(picInfo.getId());
            result.setCameraId(picInfo.getCameraId());
            result.setCameraName(stats.getCameraName());
            result.setFaceImageId(picInfo.getFaceImageId());
            result.setName(record.getName());
            result.setIdCard(record.getIdCard());
            result.setSimilarity(record.getSimilarity());
            result.setAppearTime(record.getAppearTime());
            result.setCompareTime(record.getCreateTime());
            result.setCompareStatus("SUCCESS");
            return result;
        }

        /**
         * 获取任务进度
         */
        public FaceCompareResponseVO getProgress() {
            FaceCompareResponseVO response = new FaceCompareResponseVO();
            response.setTaskId(taskId);
            response.setStartTime(startTime);
            response.setEndTime(endTime);
            response.setTotalPictures(totalPictures.get());
            response.setProcessedPictures(processedPictures.get());
            response.setSuccessCount(successCount.get());
            response.setFailedCount(failedCount.get());
            response.setErrorMessage(errorMessage);
            response.setCameraStats(new HashMap<>(cameraStats));

            // 计算总体进度
            int total = totalPictures.get();
            int processed = processedPictures.get();
            if (total > 0) {
                response.setProgressPercentage((double) processed / total * 100);
            } else {
                response.setProgressPercentage(0.0);
            }

            // 确定状态
            if (errorMessage != null) {
                response.setStatus("FAILED");
            } else if (endTime != null) {
                response.setStatus("COMPLETED");
            } else if (stopped) {
                response.setStatus("STOPPED");
            } else {
                response.setStatus("RUNNING");
            }

            // 如果任务完成，添加结果
            if ("COMPLETED".equals(response.getStatus()) || "FAILED".equals(response.getStatus())) {
                response.setResults(new ArrayList<>(results));
            }

            return response;
        }
    }
}
