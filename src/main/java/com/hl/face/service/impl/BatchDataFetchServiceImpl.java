package com.hl.face.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.face.domain.WjFaceCameraInfo;
import com.hl.face.domain.WjFacePicInfo;
import com.hl.face.domain.dto.BatchDataFetchRequestDTO;
import com.hl.face.domain.dto.FaceCompareRequestDTO;
import com.hl.face.domain.vo.BatchDataFetchResponseVO;
import com.hl.face.domain.vo.YtCameraVO;
import com.hl.face.service.BatchDataFetchService;
import com.hl.face.service.WjFaceCameraInfoService;
import com.hl.face.service.WjFacePicInfoService;
import com.hl.face.utils.YTApiUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 批量数据获取服务实现
 * 
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class BatchDataFetchServiceImpl implements BatchDataFetchService {
    
    private final WjFaceCameraInfoService wjFaceCameraInfoService;
    private final WjFacePicInfoService wjFacePicInfoService;
    private final ApplicationContext applicationContext;
    private final Executor pictureDownloadExecutor;

    public BatchDataFetchServiceImpl(WjFaceCameraInfoService wjFaceCameraInfoService,
                                   WjFacePicInfoService wjFacePicInfoService,
                                   ApplicationContext applicationContext,
                                   @Qualifier("pictureDownloadExecutor") Executor pictureDownloadExecutor) {
        this.wjFaceCameraInfoService = wjFaceCameraInfoService;
        this.wjFacePicInfoService = wjFacePicInfoService;
        this.applicationContext = applicationContext;
        this.pictureDownloadExecutor = pictureDownloadExecutor;
    }
    
    @Value("${face-compare.get-picture.cluster-id}")
    private String clusterId;
    
    @Value("${face-compare.get-picture.enable:true}")
    private Boolean isGetPictureEnabled;
    
    // 任务存储
    private final Map<String, BatchDataFetchTask> activeTasks = new ConcurrentHashMap<>();
    private final Map<String, BatchDataFetchResponseVO> completedTasks = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        log.info("批量数据获取服务初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("正在关闭批量数据获取服务...");
        // 停止所有活跃任务
        activeTasks.values().forEach(task -> {
            try {
                task.stop();
            } catch (Exception e) {
                log.error("停止任务失败: {}", task.getTaskId(), e);
            }
        });
        activeTasks.clear();
        log.info("批量数据获取服务已关闭");
    }

    @Override
    public BatchDataFetchResponseVO startBatchDataFetchTask(BatchDataFetchRequestDTO request) {
        if (!isGetPictureEnabled) {
            throw new RuntimeException("批量数据获取功能未启用");
        }
        
        // 生成任务ID
        String taskId = IdUtil.simpleUUID();
        log.info("开始启动批量数据获取任务，任务ID: {}", taskId);
        
        // 验证参数
        validateRequest(request);
        
        // 创建任务
        BatchDataFetchTask task = new BatchDataFetchTask(taskId, request);
        activeTasks.put(taskId, task);
        
        // 创建响应对象
        BatchDataFetchResponseVO response = new BatchDataFetchResponseVO();
        response.setTaskId(taskId);
        response.setStatus("RUNNING");
        response.setStartTime(new Date());
        response.setProgressPercentage(0.0);
        response.setTotalCameras(request.getCameraIds().size());
        response.setProcessedCameras(0);
        response.setTotalPictures(0);
        response.setNewPictures(0);
        response.setDuplicatePictures(0);
        response.setCompareTaskStarted(false);
        
        // 异步执行任务
        if (request.getAsync()) {
            CompletableFuture.runAsync(() -> executeTask(task), pictureDownloadExecutor);
            log.info("任务 {} 已提交到异步执行队列", taskId);
        } else {
            executeTask(task);
        }
        
        return response;
    }

    @Override
    public BatchDataFetchResponseVO getTaskProgress(String taskId) {
        BatchDataFetchTask task = activeTasks.get(taskId);
        if (task != null) {
            return task.getProgress();
        }
        
        // 检查已完成的任务
        BatchDataFetchResponseVO completedTask = completedTasks.get(taskId);
        if (completedTask != null) {
            return completedTask;
        }
        
        throw new RuntimeException("任务不存在: " + taskId);
    }

    @Override
    public boolean stopTask(String taskId) {
        BatchDataFetchTask task = activeTasks.get(taskId);
        if (task != null) {
            log.info("正在停止任务: {}", taskId);
            task.stop();
            activeTasks.remove(taskId);
            return true;
        }
        return false;
    }

    @Override
    public BatchDataFetchResponseVO getTaskResult(String taskId) {
        return getTaskProgress(taskId);
    }

    @Override
    public List<BatchDataFetchResponseVO> getActiveTasks() {
        List<BatchDataFetchResponseVO> result = new ArrayList<>();
        for (BatchDataFetchTask task : activeTasks.values()) {
            result.add(task.getProgress());
        }
        return result;
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(BatchDataFetchRequestDTO request) {
        if (request.getCameraIds() == null || request.getCameraIds().isEmpty()) {
            throw new IllegalArgumentException("相机ID列表不能为空");
        }
        
        if (request.getStartTimestamp() == null || request.getEndTimestamp() == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        
        if (request.getStartTimestamp() >= request.getEndTimestamp()) {
            throw new IllegalArgumentException("开始时间必须小于结束时间");
        }
        
        if (request.getThreadCount() != null && request.getThreadCount() <= 0) {
            throw new IllegalArgumentException("线程数量必须大于0");
        }
        
        if (request.getBatchSize() != null && request.getBatchSize() <= 0) {
            throw new IllegalArgumentException("批次大小必须大于0");
        }
        
        if (request.getMaxPages() != null && request.getMaxPages() <= 0) {
            throw new IllegalArgumentException("最大页数必须大于0");
        }
        
        log.info("请求参数验证通过 - 相机数量: {}, 时间范围: {} - {}, 线程数: {}", 
                request.getCameraIds().size(), 
                request.getStartTimestamp(), 
                request.getEndTimestamp(),
                request.getThreadCount());
    }
    
    /**
     * 执行批量数据获取任务
     */
    private void executeTask(BatchDataFetchTask task) {
        String taskId = task.getTaskId();
        BatchDataFetchRequestDTO request = task.getRequest();

        try {
            log.info("任务 {} 开始执行批量数据获取", taskId);

            // 验证相机ID是否存在
            List<WjFaceCameraInfo> validCameras = validateCameraIds(request.getCameraIds());
            if (validCameras.isEmpty()) {
                throw new RuntimeException("没有找到有效的相机");
            }

            log.info("任务 {} 找到 {} 个有效相机", taskId, validCameras.size());

            // 为每个相机创建处理任务
            List<CompletableFuture<Void>> cameraFutures = new ArrayList<>();

            for (WjFaceCameraInfo camera : validCameras) {
                if (task.isStopped()) {
                    log.info("任务 {} 已停止，跳过相机 {}", taskId, camera.getCameraId());
                    break;
                }

                CompletableFuture<Void> cameraFuture = CompletableFuture.runAsync(() ->
                        processCameraData(task, camera), pictureDownloadExecutor);
                cameraFutures.add(cameraFuture);
            }

            // 等待所有相机处理完成
            CompletableFuture.allOf(cameraFutures.toArray(new CompletableFuture[0])).join();

            if (!task.isStopped()) {
                // 任务完成
                task.markAsCompleted();

                // 如果需要立即进行人脸比对
                if (request.getImmediateCompare() && task.getTotalNewPictures() > 0) {
                    startFaceCompareTask(task);
                }

                // 移动到已完成任务
                completedTasks.put(taskId, task.getProgress());
                activeTasks.remove(taskId);

                log.info("任务 {} 执行完成，共获取 {} 张新图片", taskId, task.getTotalNewPictures());
            }

        } catch (Exception e) {
            log.error("任务 {} 执行失败", taskId, e);
            task.markAsFailed(e.getMessage());
            completedTasks.put(taskId, task.getProgress());
            activeTasks.remove(taskId);
        }
    }

    /**
     * 验证相机ID是否存在
     */
    private List<WjFaceCameraInfo> validateCameraIds(List<String> cameraIds) {
        List<WjFaceCameraInfo> validCameras = new ArrayList<>();

        for (String cameraId : cameraIds) {
            WjFaceCameraInfo camera = wjFaceCameraInfoService.getOne(
                    Wrappers.<WjFaceCameraInfo>lambdaQuery()
                            .eq(WjFaceCameraInfo::getCameraId, cameraId)
                            .eq(WjFaceCameraInfo::getEnabled, 1)
            );

            if (camera != null) {
                validCameras.add(camera);
            } else {
                log.warn("相机ID {} 不存在或未启用", cameraId);
            }
        }

        return validCameras;
    }

    /**
     * 处理单个相机的数据获取
     */
    private void processCameraData(BatchDataFetchTask task, WjFaceCameraInfo camera) {
        String taskId = task.getTaskId();
        String cameraId = camera.getCameraId();
        BatchDataFetchRequestDTO request = task.getRequest();

        try {
            log.info("任务 {} 开始处理相机 {} 的数据", taskId, cameraId);

            // 更新相机状态为处理中
            BatchDataFetchResponseVO.CameraFetchStats stats = task.getCameraStats(cameraId);
            stats.setCameraName(camera.getName());
            stats.setStatus("RUNNING");

            // 获取该相机在指定时间范围内的数据
            long startTime = request.getStartTimestamp();
            long endTime = request.getEndTimestamp();

            List<YtCameraVO> allCameraVOList = new ArrayList<>();
            int pageNum = 1;
            int maxPages = request.getMaxPages();

            while (pageNum <= maxPages && !task.isStopped()) {
                log.debug("任务 {} 相机 {} 正在获取第 {} 页数据", taskId, cameraId, pageNum);

                JSONObject pictureInfo = YTApiUtils.postDevicePictureInfo(
                        clusterId,
                        Arrays.asList(cameraId),
                        startTime
                );

                List<YtCameraVO> cameraVOList = pictureInfo.getList("results", YtCameraVO.class);
                if (ObjectUtil.isNull(cameraVOList) || cameraVOList.isEmpty()) {
                    log.info("任务 {} 相机 {} 第 {} 页数据为空，结束获取", taskId, cameraId, pageNum);
                    break;
                }

                // 过滤时间范围
                List<YtCameraVO> filteredList = new ArrayList<>();
                for (YtCameraVO vo : cameraVOList) {
                    if (vo.getTimestamp() >= request.getStartTimestamp() &&
                        vo.getTimestamp() <= request.getEndTimestamp()) {
                        filteredList.add(vo);
                    }
                }

                if (filteredList.isEmpty()) {
                    log.info("任务 {} 相机 {} 第 {} 页过滤后数据为空", taskId, cameraId, pageNum);
                    break;
                }

                allCameraVOList.addAll(filteredList);
                log.debug("任务 {} 相机 {} 第 {} 页获取到 {} 条数据，累计 {} 条",
                        taskId, cameraId, pageNum, filteredList.size(), allCameraVOList.size());

                // 如果返回数据不足100条，说明已经获取完所有数据
                if (cameraVOList.size() < 100) {
                    log.info("任务 {} 相机 {} 数据获取完毕，共 {} 页 {} 条数据",
                            taskId, cameraId, pageNum, allCameraVOList.size());
                    break;
                }

                // 更新startTime为最后一条记录的时间戳
                startTime = cameraVOList.get(cameraVOList.size() - 1).getTimestamp();
                pageNum++;

                // 更新统计信息
                stats.setFetchedPages(pageNum - 1);
                stats.setTotalPictures(allCameraVOList.size());
            }

            // 保存图片信息到数据库
            int newPictures = savePictureInfo(taskId, cameraId, allCameraVOList);
            int duplicatePictures = allCameraVOList.size() - newPictures;

            // 更新统计信息
            stats.setNewPictures(newPictures);
            stats.setDuplicatePictures(duplicatePictures);
            stats.setStatus("COMPLETED");
            stats.setProgress(100.0);
            if (!allCameraVOList.isEmpty()) {
                stats.setLatestTimestamp(allCameraVOList.get(allCameraVOList.size() - 1).getTimestamp());
            }

            // 更新任务总体统计
            task.addProcessedCamera();
            task.addNewPictures(newPictures);
            task.addDuplicatePictures(duplicatePictures);

            log.info("任务 {} 相机 {} 处理完成，获取 {} 张图片，新增 {} 张",
                    taskId, cameraId, allCameraVOList.size(), newPictures);

        } catch (Exception e) {
            log.error("任务 {} 处理相机 {} 失败", taskId, cameraId, e);

            BatchDataFetchResponseVO.CameraFetchStats stats = task.getCameraStats(cameraId);
            stats.setStatus("FAILED");
            stats.setErrorMessage(e.getMessage());

            task.addProcessedCamera();
        }
    }

    /**
     * 保存图片信息到数据库
     */
    private int savePictureInfo(String taskId, String cameraId, List<YtCameraVO> cameraVOList) {
        int newPictures = 0;

        for (YtCameraVO cameraVO : cameraVOList) {
            try {
                String faceImageId = cameraVO.getFaceImageId();

                // 检验有没有保存过
                WjFacePicInfo existing = wjFacePicInfoService.getOne(
                        Wrappers.<WjFacePicInfo>lambdaQuery()
                                .eq(WjFacePicInfo::getFaceImageId, faceImageId)
                );

                if (existing != null) {
                    continue; // 已存在，跳过
                }

                // 创建新的图片信息记录
                WjFacePicInfo wjFacePicInfo = new WjFacePicInfo();
                wjFacePicInfo.setCameraId(cameraId);
                wjFacePicInfo.setFaceImageId(faceImageId);
                wjFacePicInfo.setGlobalPictureUri(cameraVO.getGlobalPictureUri());
                wjFacePicInfo.setGlobalFaceImageUri(cameraVO.getGlobalFaceImageUri());
                wjFacePicInfo.setStatus(0); // 0表示未比对
                wjFacePicInfo.setTimestamp(cameraVO.getTimestamp());

                wjFacePicInfoService.save(wjFacePicInfo);
                newPictures++;

                log.debug("任务 {} 相机 {} 保存新图片: {}", taskId, cameraId, faceImageId);

            } catch (Exception e) {
                log.error("任务 {} 相机 {} 保存图片信息失败: {}", taskId, cameraId, cameraVO.getFaceImageId(), e);
            }
        }

        return newPictures;
    }

    /**
     * 启动人脸比对任务
     */
    private void startFaceCompareTask(BatchDataFetchTask task) {
        try {
            // 使用ApplicationContext延迟获取MultiFaceCompareService，避免循环依赖
            Object multiFaceCompareService = applicationContext.getBean("multiFaceCompareServiceImpl");
            if (multiFaceCompareService == null) {
                log.warn("任务 {} 无法获取MultiFaceCompareService，跳过人脸比对", task.getTaskId());
                return;
            }

            BatchDataFetchRequestDTO request = task.getRequest();

            // 创建人脸比对请求
            FaceCompareRequestDTO compareRequest = new FaceCompareRequestDTO();
            compareRequest.setCameraIds(request.getCameraIds());
            compareRequest.setStartTimestamp(request.getStartTimestamp());
            compareRequest.setEndTimestamp(request.getEndTimestamp());
            compareRequest.setThreadCount(request.getThreadCount());
            compareRequest.setBatchSize(request.getBatchSize());
            compareRequest.setAsync(true); // 异步执行

            // 使用反射调用startFaceCompareTask方法
            try {
                java.lang.reflect.Method method = multiFaceCompareService.getClass().getMethod("startFaceCompareTask", FaceCompareRequestDTO.class);
                Object compareResponse = method.invoke(multiFaceCompareService, compareRequest);

                // 获取taskId
                java.lang.reflect.Method getTaskIdMethod = compareResponse.getClass().getMethod("getTaskId");
                String compareTaskId = (String) getTaskIdMethod.invoke(compareResponse);

                task.setCompareTaskId(compareTaskId);
                task.setCompareTaskStarted(true);

                log.info("任务 {} 已启动人脸比对任务: {}", task.getTaskId(), compareTaskId);

            } catch (Exception reflectionException) {
                log.error("任务 {} 反射调用人脸比对服务失败", task.getTaskId(), reflectionException);
            }

        } catch (Exception e) {
            log.error("任务 {} 启动人脸比对任务失败", task.getTaskId(), e);
        }
    }

    /**
     * 批量数据获取任务类
     */
    private static class BatchDataFetchTask {
        private final String taskId;
        private final BatchDataFetchRequestDTO request;
        private final Date startTime;
        private Date endTime;
        private volatile boolean stopped = false;
        private String errorMessage;

        private final AtomicInteger processedCameras = new AtomicInteger(0);
        private final AtomicInteger totalNewPictures = new AtomicInteger(0);
        private final AtomicInteger totalDuplicatePictures = new AtomicInteger(0);

        private final Map<String, BatchDataFetchResponseVO.CameraFetchStats> cameraStats = new ConcurrentHashMap<>();

        private boolean compareTaskStarted = false;
        private String compareTaskId;

        public BatchDataFetchTask(String taskId, BatchDataFetchRequestDTO request) {
            this.taskId = taskId;
            this.request = request;
            this.startTime = new Date();

            // 初始化相机统计
            for (String cameraId : request.getCameraIds()) {
                BatchDataFetchResponseVO.CameraFetchStats stats = new BatchDataFetchResponseVO.CameraFetchStats();
                stats.setCameraId(cameraId);
                stats.setFetchedPages(0);
                stats.setTotalPictures(0);
                stats.setNewPictures(0);
                stats.setDuplicatePictures(0);
                stats.setProgress(0.0);
                stats.setStatus("PENDING");
                cameraStats.put(cameraId, stats);
            }
        }

        public String getTaskId() {
            return taskId;
        }

        public BatchDataFetchRequestDTO getRequest() {
            return request;
        }

        public boolean isStopped() {
            return stopped;
        }

        public void stop() {
            this.stopped = true;
            log.info("批量数据获取任务 {} 已标记为停止", taskId);
        }

        public void markAsCompleted() {
            this.endTime = new Date();
        }

        public void markAsFailed(String errorMessage) {
            this.errorMessage = errorMessage;
            this.endTime = new Date();
        }

        public BatchDataFetchResponseVO.CameraFetchStats getCameraStats(String cameraId) {
            return cameraStats.get(cameraId);
        }

        public void addProcessedCamera() {
            processedCameras.incrementAndGet();
        }

        public void addNewPictures(int count) {
            totalNewPictures.addAndGet(count);
        }

        public void addDuplicatePictures(int count) {
            totalDuplicatePictures.addAndGet(count);
        }

        public int getTotalNewPictures() {
            return totalNewPictures.get();
        }

        public void setCompareTaskStarted(boolean compareTaskStarted) {
            this.compareTaskStarted = compareTaskStarted;
        }

        public void setCompareTaskId(String compareTaskId) {
            this.compareTaskId = compareTaskId;
        }

        public BatchDataFetchResponseVO getProgress() {
            BatchDataFetchResponseVO response = new BatchDataFetchResponseVO();
            response.setTaskId(taskId);
            response.setStartTime(startTime);
            response.setEndTime(endTime);
            response.setTotalCameras(request.getCameraIds().size());
            response.setProcessedCameras(processedCameras.get());
            response.setTotalPictures(totalNewPictures.get() + totalDuplicatePictures.get());
            response.setNewPictures(totalNewPictures.get());
            response.setDuplicatePictures(totalDuplicatePictures.get());
            response.setCompareTaskStarted(compareTaskStarted);
            response.setCompareTaskId(compareTaskId);
            response.setCameraStats(new HashMap<>(cameraStats));
            response.setErrorMessage(errorMessage);

            // 计算进度
            if (request.getCameraIds().size() > 0) {
                double progress = (double) processedCameras.get() / request.getCameraIds().size() * 100.0;
                response.setProgressPercentage(progress);
            } else {
                response.setProgressPercentage(0.0);
            }

            // 设置状态
            if (stopped) {
                response.setStatus("STOPPED");
            } else if (errorMessage != null) {
                response.setStatus("FAILED");
            } else if (endTime != null) {
                response.setStatus("COMPLETED");
            } else {
                response.setStatus("RUNNING");
            }

            return response;
        }
    }
}
