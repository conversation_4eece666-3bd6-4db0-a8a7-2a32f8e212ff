package com.hl.face.service;

import com.hl.face.domain.dto.BatchDataFetchRequestDTO;
import com.hl.face.domain.vo.BatchDataFetchResponseVO;

import java.util.List;

/**
 * 批量数据获取服务接口
 *
 * <AUTHOR> Assistant
 */
public interface BatchDataFetchService {

    /**
     * 启动批量数据获取任务
     *
     * @param request 批量数据获取请求参数
     * @return 任务响应信息
     */
    BatchDataFetchResponseVO startBatchDataFetchTask(BatchDataFetchRequestDTO request);

    /**
     * 查询任务进度
     *
     * @param taskId 任务ID
     * @return 任务进度信息
     */
    BatchDataFetchResponseVO getTaskProgress(String taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务ID
     * @return 是否成功停止
     */
    boolean stopTask(String taskId);

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    BatchDataFetchResponseVO getTaskResult(String taskId);

    /**
     * 获取所有活跃任务
     *
     * @return 活跃任务列表
     */
    List<BatchDataFetchResponseVO> getActiveTasks();
}
