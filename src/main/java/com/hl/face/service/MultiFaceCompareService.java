package com.hl.face.service;

import com.hl.face.domain.dto.FaceCompareRequestDTO;
import com.hl.face.domain.vo.FaceCompareResponseVO;

import java.util.List;

/**
 * 多线程人脸比对服务接口
 *
 * <AUTHOR> Assistant
 */
public interface MultiFaceCompareService {

    /**
     * 启动人脸比对任务
     *
     * @param request 比对请求参数
     * @return 任务响应信息
     */
    FaceCompareResponseVO startFaceCompareTask(FaceCompareRequestDTO request);

    /**
     * 查询任务进度
     *
     * @param taskId 任务ID
     * @return 任务进度信息
     */
    FaceCompareResponseVO getTaskProgress(String taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务ID
     * @return 是否成功停止
     */
    boolean stopTask(String taskId);

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    FaceCompareResponseVO getTaskResult(String taskId);

    /**
     * 获取所有活跃任务
     *
     * @return 活跃任务列表
     */
    List<FaceCompareResponseVO> getActiveTasks();
}
