package com.hl.face.controller;

import com.hl.common.domain.R;
import com.hl.face.domain.dto.BatchDataFetchRequestDTO;
import com.hl.face.domain.vo.BatchDataFetchResponseVO;
import com.hl.face.service.BatchDataFetchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 批量数据获取控制器
 * 
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/face/batch")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "批量数据获取")
@Validated
public class BatchDataFetchController {
    
    private final BatchDataFetchService batchDataFetchService;
    
    /**
     * 启动批量数据获取任务
     */
    @PostMapping("/fetch/start")
    @ApiOperation("启动批量数据获取任务")
    public R<BatchDataFetchResponseVO> startBatchDataFetchTask(
            @ApiParam(value = "批量数据获取请求参数", required = true)
            @Valid @RequestBody BatchDataFetchRequestDTO request) {
        
        log.info("收到批量数据获取请求，相机数量: {}, 时间范围: {} - {}, 立即比对: {}", 
                request.getCameraIds().size(), 
                request.getStartTimestamp(), 
                request.getEndTimestamp(),
                request.getImmediateCompare());
        
        try {
            BatchDataFetchResponseVO response = batchDataFetchService.startBatchDataFetchTask(request);
            log.info("批量数据获取任务启动成功，任务ID: {}", response.getTaskId());
            return R.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.warn("批量数据获取请求参数错误: {}", e.getMessage());
            return R.fail(400, e.getMessage());
            
        } catch (Exception e) {
            log.error("启动批量数据获取任务失败", e);
            return R.fail(500, "启动批量数据获取任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务进度
     */
    @GetMapping("/fetch/progress/{taskId}")
    @ApiOperation("查询批量数据获取任务进度")
    public R<BatchDataFetchResponseVO> getTaskProgress(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.debug("查询批量数据获取任务进度，任务ID: {}", taskId);
        
        try {
            BatchDataFetchResponseVO response = batchDataFetchService.getTaskProgress(taskId);
            return R.ok(response);
            
        } catch (RuntimeException e) {
            log.warn("查询批量数据获取任务进度失败: {}", e.getMessage());
            return R.fail(404, e.getMessage());
            
        } catch (Exception e) {
            log.error("查询批量数据获取任务进度异常，任务ID: {}", taskId, e);
            return R.fail(500, "查询任务进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止任务
     */
    @PostMapping("/fetch/stop/{taskId}")
    @ApiOperation("停止批量数据获取任务")
    public R<Boolean> stopTask(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("收到停止批量数据获取任务请求，任务ID: {}", taskId);
        
        try {
            boolean result = batchDataFetchService.stopTask(taskId);
            if (result) {
                log.info("批量数据获取任务停止成功，任务ID: {}", taskId);
                return R.ok(true, "任务停止成功");
            } else {
                log.warn("批量数据获取任务停止失败，任务不存在或已完成，任务ID: {}", taskId);
                return R.fail(404, "任务不存在或已完成");
            }
            
        } catch (Exception e) {
            log.error("停止批量数据获取任务异常，任务ID: {}", taskId, e);
            return R.fail(500, "停止任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务结果
     */
    @GetMapping("/fetch/result/{taskId}")
    @ApiOperation("获取批量数据获取任务结果")
    public R<BatchDataFetchResponseVO> getTaskResult(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("查询批量数据获取任务结果，任务ID: {}", taskId);
        
        try {
            BatchDataFetchResponseVO response = batchDataFetchService.getTaskResult(taskId);
            return R.ok(response);
            
        } catch (RuntimeException e) {
            log.warn("查询批量数据获取任务结果失败: {}", e.getMessage());
            return R.fail(404, e.getMessage());
            
        } catch (Exception e) {
            log.error("查询批量数据获取任务结果异常，任务ID: {}", taskId, e);
            return R.fail(500, "查询任务结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有活跃任务
     */
    @GetMapping("/fetch/active")
    @ApiOperation("获取所有活跃的批量数据获取任务")
    public R<List<BatchDataFetchResponseVO>> getActiveTasks() {
        
        log.debug("查询所有活跃的批量数据获取任务");
        
        try {
            List<BatchDataFetchResponseVO> activeTasks = batchDataFetchService.getActiveTasks();
            log.debug("当前活跃的批量数据获取任务数量: {}", activeTasks.size());
            return R.ok(activeTasks);
            
        } catch (Exception e) {
            log.error("查询活跃的批量数据获取任务异常", e);
            return R.fail(500, "查询活跃任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/fetch/health")
    @ApiOperation("批量数据获取服务健康检查")
    public R<String> healthCheck() {
        return R.ok("批量数据获取服务运行正常");
    }
}
