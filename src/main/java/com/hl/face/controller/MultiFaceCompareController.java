package com.hl.face.controller;

import com.hl.common.domain.R;
import com.hl.face.domain.dto.BatchDataFetchRequestDTO;
import com.hl.face.domain.dto.FaceCompareRequestDTO;
import com.hl.face.domain.vo.BatchDataFetchResponseVO;
import com.hl.face.domain.vo.FaceCompareResponseVO;
import com.hl.face.service.BatchDataFetchService;
import com.hl.face.service.MultiFaceCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 多线程人脸比对控制器
 * 
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/face/compare")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "多线程人脸比对")
@Validated
public class MultiFaceCompareController {
    
    private final MultiFaceCompareService multiFaceCompareService;
    private final BatchDataFetchService batchDataFetchService;
    
    /**
     * 启动人脸比对任务
     */
    @PostMapping("/start")
    @ApiOperation("启动人脸比对任务")
    public R<FaceCompareResponseVO> startFaceCompareTask(
            @ApiParam(value = "人脸比对请求参数", required = true)
            @Valid @RequestBody FaceCompareRequestDTO request) {
        
        log.info("收到人脸比对请求，相机数量: {}, 时间范围: {} - {}, 线程数: {}", 
                request.getCameraIds().size(), 
                request.getStartTimestamp(), 
                request.getEndTimestamp(),
                request.getThreadCount());
        
        try {
            FaceCompareResponseVO response = multiFaceCompareService.startFaceCompareTask(request);
            log.info("人脸比对任务启动成功，任务ID: {}", response.getTaskId());
            return R.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.warn("人脸比对请求参数错误: {}", e.getMessage());
            return R.fail(400, e.getMessage());
            
        } catch (Exception e) {
            log.error("启动人脸比对任务失败", e);
            return R.fail(500, "启动人脸比对任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动批量数据获取和人脸比对的综合任务
     */
    @PostMapping("/batch-fetch-and-compare")
    @ApiOperation("启动批量数据获取和人脸比对的综合任务")
    public R<BatchDataFetchResponseVO> startBatchFetchAndCompareTask(
            @ApiParam(value = "批量数据获取请求参数", required = true)
            @Valid @RequestBody BatchDataFetchRequestDTO request) {
        
        log.info("收到批量数据获取和比对请求，相机数量: {}, 时间范围: {} - {}, 立即比对: {}", 
                request.getCameraIds().size(), 
                request.getStartTimestamp(), 
                request.getEndTimestamp(),
                request.getImmediateCompare());
        
        try {
            // 确保立即比对设置为true
            request.setImmediateCompare(true);
            
            BatchDataFetchResponseVO response = batchDataFetchService.startBatchDataFetchTask(request);
            log.info("批量数据获取和比对任务启动成功，任务ID: {}", response.getTaskId());
            return R.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.warn("批量数据获取和比对请求参数错误: {}", e.getMessage());
            return R.fail(400, e.getMessage());
            
        } catch (Exception e) {
            log.error("启动批量数据获取和比对任务失败", e);
            return R.fail(500, "启动批量数据获取和比对任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务进度
     */
    @GetMapping("/progress/{taskId}")
    @ApiOperation("查询任务进度")
    public R<FaceCompareResponseVO> getTaskProgress(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.debug("查询任务进度，任务ID: {}", taskId);
        
        try {
            FaceCompareResponseVO response = multiFaceCompareService.getTaskProgress(taskId);
            return R.ok(response);
            
        } catch (RuntimeException e) {
            log.warn("查询任务进度失败: {}", e.getMessage());
            return R.fail(404, e.getMessage());
            
        } catch (Exception e) {
            log.error("查询任务进度异常，任务ID: {}", taskId, e);
            return R.fail(500, "查询任务进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止任务
     */
    @PostMapping("/stop/{taskId}")
    @ApiOperation("停止任务")
    public R<Boolean> stopTask(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("收到停止任务请求，任务ID: {}", taskId);
        
        try {
            boolean result = multiFaceCompareService.stopTask(taskId);
            if (result) {
                log.info("任务停止成功，任务ID: {}", taskId);
                return R.ok(true, "任务停止成功");
            } else {
                log.warn("任务停止失败，任务不存在或已完成，任务ID: {}", taskId);
                return R.fail(404, "任务不存在或已完成");
            }
            
        } catch (Exception e) {
            log.error("停止任务异常，任务ID: {}", taskId, e);
            return R.fail(500, "停止任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务结果
     */
    @GetMapping("/result/{taskId}")
    @ApiOperation("获取任务结果")
    public R<FaceCompareResponseVO> getTaskResult(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("查询任务结果，任务ID: {}", taskId);
        
        try {
            FaceCompareResponseVO response = multiFaceCompareService.getTaskResult(taskId);
            return R.ok(response);
            
        } catch (RuntimeException e) {
            log.warn("查询任务结果失败: {}", e.getMessage());
            return R.fail(404, e.getMessage());
            
        } catch (Exception e) {
            log.error("查询任务结果异常，任务ID: {}", taskId, e);
            return R.fail(500, "查询任务结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有活跃任务
     */
    @GetMapping("/active")
    @ApiOperation("获取所有活跃任务")
    public R<List<FaceCompareResponseVO>> getActiveTasks() {
        
        log.debug("查询所有活跃任务");
        
        try {
            List<FaceCompareResponseVO> activeTasks = multiFaceCompareService.getActiveTasks();
            log.debug("当前活跃任务数量: {}", activeTasks.size());
            return R.ok(activeTasks);
            
        } catch (Exception e) {
            log.error("查询活跃任务异常", e);
            return R.fail(500, "查询活跃任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public R<String> healthCheck() {
        return R.ok("人脸比对服务运行正常");
    }
}
