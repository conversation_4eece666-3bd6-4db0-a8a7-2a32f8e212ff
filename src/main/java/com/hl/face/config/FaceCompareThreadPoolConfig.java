package com.hl.face.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 人脸比对线程池配置
 * 
 * <AUTHOR> Assistant
 */
@Configuration
@EnableAsync
@Slf4j
public class FaceCompareThreadPoolConfig {
    
    @Value("${face-compare.thread-pool.core-pool-size:20}")
    private int corePoolSize;
    
    @Value("${face-compare.thread-pool.max-pool-size:50}")
    private int maxPoolSize;
    
    @Value("${face-compare.thread-pool.queue-capacity:1000}")
    private int queueCapacity;
    
    @Value("${face-compare.thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;
    
    /**
     * 人脸比对专用线程池
     */
    @Bean("faceCompareExecutor")
    public Executor faceCompareExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        
        // 队列容量
        executor.setQueueCapacity(queueCapacity);
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        
        // 线程名前缀
        executor.setThreadNamePrefix("FaceCompare-");
        
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        log.info("人脸比对线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }
    
    /**
     * 图片下载专用线程池
     */
    @Bean("pictureDownloadExecutor")
    public Executor pictureDownloadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 图片下载使用较小的线程池
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("PictureDownload-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        
        log.info("图片下载线程池初始化完成 - 核心线程数: 10, 最大线程数: 20");
        
        return executor;
    }
}
