package com.hl.face.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量数据获取请求DTO
 * 
 * <AUTHOR> Assistant
 */
@Data
@ApiModel("批量数据获取请求参数")
public class BatchDataFetchRequestDTO {
    
    /**
     * 相机ID列表
     */
    @ApiModelProperty(value = "相机ID列表", required = true, example = "[\"camera1\", \"camera2\"]")
    @NotEmpty(message = "相机ID列表不能为空")
    private List<String> cameraIds;
    
    /**
     * 开始时间戳（秒）
     */
    @ApiModelProperty(value = "开始时间戳（秒）", required = true, example = "1640995200")
    @NotNull(message = "开始时间不能为空")
    private Long startTimestamp;
    
    /**
     * 结束时间戳（秒）
     */
    @ApiModelProperty(value = "结束时间戳（秒）", required = true, example = "1641081600")
    @NotNull(message = "结束时间不能为空")
    private Long endTimestamp;
    
    /**
     * 是否立即进行人脸比对（可选，默认为true）
     */
    @ApiModelProperty(value = "是否立即进行人脸比对", example = "true")
    private Boolean immediateCompare = true;
    
    /**
     * 线程数量（可选，默认为10）
     */
    @ApiModelProperty(value = "线程数量", example = "10")
    private Integer threadCount = 10;
    
    /**
     * 批次大小（可选，默认为100）
     */
    @ApiModelProperty(value = "批次大小", example = "100")
    private Integer batchSize = 100;
    
    /**
     * 是否异步执行（可选，默认为true）
     */
    @ApiModelProperty(value = "是否异步执行", example = "true")
    private Boolean async = true;
    
    /**
     * 最大获取页数限制（可选，默认为50）
     */
    @ApiModelProperty(value = "最大获取页数限制", example = "50")
    private Integer maxPages = 50;
}
