package com.hl.face.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 人脸比对响应VO
 * 
 * <AUTHOR> Assistant
 */
@Data
@ApiModel("人脸比对响应结果")
public class FaceCompareResponseVO {
    
    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String taskId;
    
    /**
     * 任务状态：RUNNING, COMPLETED, FAILED
     */
    @ApiModelProperty("任务状态")
    private String status;
    
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;
    
    /**
     * 总图片数量
     */
    @ApiModelProperty("总图片数量")
    private Integer totalPictures;
    
    /**
     * 已处理图片数量
     */
    @ApiModelProperty("已处理图片数量")
    private Integer processedPictures;
    
    /**
     * 成功比对数量
     */
    @ApiModelProperty("成功比对数量")
    private Integer successCount;
    
    /**
     * 失败比对数量
     */
    @ApiModelProperty("失败比对数量")
    private Integer failedCount;
    
    /**
     * 处理进度百分比
     */
    @ApiModelProperty("处理进度百分比")
    private Double progressPercentage;
    
    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    /**
     * 每个相机的处理统计
     */
    @ApiModelProperty("每个相机的处理统计")
    private Map<String, CameraProcessStats> cameraStats;
    
    /**
     * 比对结果列表（仅在任务完成时返回）
     */
    @ApiModelProperty("比对结果列表")
    private List<FaceCompareResultVO> results;
    
    /**
     * 相机处理统计
     */
    @Data
    @ApiModel("相机处理统计")
    public static class CameraProcessStats {
        
        @ApiModelProperty("相机ID")
        private String cameraId;
        
        @ApiModelProperty("相机名称")
        private String cameraName;
        
        @ApiModelProperty("总图片数")
        private Integer totalPictures;
        
        @ApiModelProperty("已处理数")
        private Integer processedPictures;
        
        @ApiModelProperty("成功数")
        private Integer successCount;
        
        @ApiModelProperty("失败数")
        private Integer failedCount;
        
        @ApiModelProperty("处理进度")
        private Double progress;
        
        @ApiModelProperty("当前状态")
        private String status;
    }
    
    /**
     * 人脸比对结果
     */
    @Data
    @ApiModel("人脸比对结果")
    public static class FaceCompareResultVO {
        
        @ApiModelProperty("图片ID")
        private String picId;
        
        @ApiModelProperty("相机ID")
        private String cameraId;
        
        @ApiModelProperty("相机名称")
        private String cameraName;
        
        @ApiModelProperty("人脸图片ID")
        private String faceImageId;
        
        @ApiModelProperty("识别姓名")
        private String name;
        
        @ApiModelProperty("身份证号")
        private String idCard;
        
        @ApiModelProperty("相似度")
        private String similarity;
        
        @ApiModelProperty("出现时间")
        private Date appearTime;
        
        @ApiModelProperty("比对时间")
        private Date compareTime;
        
        @ApiModelProperty("比对状态")
        private String compareStatus;
        
        @ApiModelProperty("错误信息")
        private String errorMessage;
    }
}
