package com.hl.face.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 批量数据获取响应VO
 * 
 * <AUTHOR> Assistant
 */
@Data
@ApiModel("批量数据获取响应结果")
public class BatchDataFetchResponseVO {
    
    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String taskId;
    
    /**
     * 任务状态：RUNNING, COMPLETED, FAILED
     */
    @ApiModelProperty("任务状态")
    private String status;
    
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;
    
    /**
     * 总相机数量
     */
    @ApiModelProperty("总相机数量")
    private Integer totalCameras;
    
    /**
     * 已处理相机数量
     */
    @ApiModelProperty("已处理相机数量")
    private Integer processedCameras;
    
    /**
     * 总获取图片数量
     */
    @ApiModelProperty("总获取图片数量")
    private Integer totalPictures;
    
    /**
     * 新增图片数量
     */
    @ApiModelProperty("新增图片数量")
    private Integer newPictures;
    
    /**
     * 重复图片数量
     */
    @ApiModelProperty("重复图片数量")
    private Integer duplicatePictures;
    
    /**
     * 处理进度百分比
     */
    @ApiModelProperty("处理进度百分比")
    private Double progressPercentage;
    
    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    /**
     * 每个相机的处理统计
     */
    @ApiModelProperty("每个相机的处理统计")
    private Map<String, CameraFetchStats> cameraStats;
    
    /**
     * 是否启动了人脸比对任务
     */
    @ApiModelProperty("是否启动了人脸比对任务")
    private Boolean compareTaskStarted;
    
    /**
     * 人脸比对任务ID（如果启动了比对任务）
     */
    @ApiModelProperty("人脸比对任务ID")
    private String compareTaskId;
    
    /**
     * 相机数据获取统计
     */
    @Data
    @ApiModel("相机数据获取统计")
    public static class CameraFetchStats {
        
        @ApiModelProperty("相机ID")
        private String cameraId;
        
        @ApiModelProperty("相机名称")
        private String cameraName;
        
        @ApiModelProperty("获取页数")
        private Integer fetchedPages;
        
        @ApiModelProperty("总图片数")
        private Integer totalPictures;
        
        @ApiModelProperty("新增图片数")
        private Integer newPictures;
        
        @ApiModelProperty("重复图片数")
        private Integer duplicatePictures;
        
        @ApiModelProperty("处理进度")
        private Double progress;
        
        @ApiModelProperty("当前状态")
        private String status;
        
        @ApiModelProperty("错误信息")
        private String errorMessage;
        
        @ApiModelProperty("最新时间戳")
        private Long latestTimestamp;
    }
    
    /**
     * 获取的图片信息摘要
     */
    @Data
    @ApiModel("图片信息摘要")
    public static class PictureSummary {
        
        @ApiModelProperty("相机ID")
        private String cameraId;
        
        @ApiModelProperty("图片数量")
        private Integer count;
        
        @ApiModelProperty("时间范围开始")
        private Long startTimestamp;
        
        @ApiModelProperty("时间范围结束")
        private Long endTimestamp;
    }
}
