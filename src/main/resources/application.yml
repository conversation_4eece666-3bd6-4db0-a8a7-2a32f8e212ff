# 项目相关配置
hualong:
  # title
  title: 智慧楼宇
  # 描述
  description: hl-building
  # 版本
  version: 0.0.1
  # 返回内容包含字段: 成功码
  success: 200
  # 返回最大字段长度
  logMaxLen: 1000
  # corePoolSize
  corePoolSize: 50

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 38081
  # GRACEFUL (优雅) 当应用程序以"GRACEFUL"模式关闭时，它不会接受新的请求且会尝试完成所有正在进行的请求和处理，然后才会终止。
  # IMMEDIATE( 立即)    当应用程序以"IMMEDIATE"模式关闭时，它会立即终止，而不管当前是否有任何活动任务或请求。
  shutdown: graceful
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.hl: debug
    org.springframework: info
  #  config: ./conf/logback.xml
  config: ./conf/log4j2.xml
  # 所有的请求日志，基于aop的写入日志进入mysql
  mysql:
    # 默认关闭
    enabled: false
    # 使用的数据源
    datasource: MASTER
    # 写入的数据库
    database: task_log
    # 日志的最大存储时长，单位天
    maxSaveDays: 90
  # 所有的请求日志，基于aop的写入日志进入Slf4j
  slf4j:
    # 默认关闭
    enabled: true
    filterUrl:

# Spring配置
spring:
  web:
    resource:
      add-mappings: true
  main:
    banner-mode: off
    # servlet, none,(Servlet、Reactive)
  #    web-application-type: none
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
    prefix: file:./config/thymeleaf/templates/
    check-template-location: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  redis:
    host: *************  # 地址
    port: 10016  # 端口号
    database: 0  # 数据库索引（默认为0）
    timeout: 3000  # 连接超时时间（毫秒）
    lettuce:
      pool:
        max-active: 20  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1  # 最大阻塞等待时间（负数表示没有限制）
        max-idle: 5  # 连接池中最大空闲连接
        min-idle: 0  # 连接池中最小空闲连接
  security:
    # 未认证是，返回的错误码，默认401
    # unauthorized: 401
    # 在header中token名称
    type: ssork
    passToken:
    sso:
      # token: token
      # sso认证地址
      url: http://*************:8184/
      # project_id
      projectId: jzzl
      # sso中项目访问的token
      projectToken: 56as4da344dc7cc4f57909fd5264588d
  application:
    name: hl-task-wj
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123
      config:
        server-addr: *************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123


# MyBatis配置
mybatis:
  druidEnabled: true
  # 搜索指定包别名
  typeAliasesPackage: com.hl.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
  global-config:
    banner: off
#  configuration:
#    map-underscore-to-camel-case: false

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /
#  pathMapping: /dev-api



face-compare:
  get-picture:
    enable: true
    cluster-id: WJSPW_HJ_1639380209
  compare-picture:
    enable: true
    cluster-id: WJSPW_HJ_1639380209
  thread-pool:
    core-pool-size: 20      # 核心线程数
    max-pool-size: 50       # 最大线程数
    queue-capacity: 1000    # 队列容量
    keep-alive-seconds: 60  # 线程空闲时间


liteflow:
  rule-source-ext-data-map:
    url: **********************************************************************************************************************************************
    driverClassName: org.postgresql.Driver
    username: hl
    password: hl123
    applicationName: demo
    #是否开启SQL日志
    sqlLogEnabled: true
    #是否开启SQL数据轮询自动刷新机制 默认不开启
    pollingEnabled: true
    pollingIntervalSeconds: 60
    pollingStartSeconds: 60
    #以下是chain表的配置，这个一定得有
    chainTableName: wjhl.wj_liteflow_chain
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
#    #以下是决策路由字段的配置，如果你没用到决策路由，可以不配置
#    routeField:
#    namespaceField:
#    #是否启用这条规则
#    chainEnableField: enable
#    #规则表自定义过滤SQL
#    chainCustomSql:
    #以下是script表的配置，如果你没使用到脚本，下面可以不配置
#    scriptTableName: script
#    scriptApplicationNameField: application_name
#    scriptIdField: script_id
#    scriptNameField: script_name
#    scriptDataField: script_data
#    scriptTypeField: script_type
#    scriptLanguageField: script_language
#    #是否启用这条脚本
#    scriptEnableField: enable
#    #脚本表自定义过滤SQL
#    scriptCustomSql:
